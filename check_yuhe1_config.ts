import { PrismaMongoClient } from 'model/mongodb/prisma'

async function checkYuhe1Config() {
  try {
    console.log('正在检查 yuhe1 配置...')
    
    // 查询 yuhe1 的配置
    const config = await PrismaMongoClient.getConfigInstance().config.findFirst({
      where: {
        enterpriseName: 'yuhe',
        accountName: 'yuhe1'
      }
    })
    
    if (config) {
      console.log('✅ 找到 yuhe1 配置:')
      console.log(`  - 账号名: ${config.accountName}`)
      console.log(`  - 微信ID: ${config.wechatId}`)
      console.log(`  - 端口: ${config.port}`)
      console.log(`  - 地址: ${config.address}`)
      console.log(`  - 企业名: ${config.enterpriseName}`)
    } else {
      console.log('❌ 未找到 yuhe1 配置')
      
      // 查询所有 yuhe 企业的配置
      console.log('\n查询所有 yuhe 企业的配置:')
      const allConfigs = await PrismaMongoClient.getConfigInstance().config.findMany({
        where: {
          enterpriseName: 'yuhe'
        },
        select: {
          accountName: true,
          wechatId: true,
          port: true
        }
      })
      
      if (allConfigs.length > 0) {
        console.log('现有配置:')
        allConfigs.forEach(cfg => {
          console.log(`  - ${cfg.accountName}: 端口 ${cfg.port}`)
        })
      } else {
        console.log('没有找到任何 yuhe 企业的配置')
      }
    }
    
  } catch (error) {
    console.error('检查配置时出错:', error)
  } finally {
    process.exit(0)
  }
}

checkYuhe1Config()
