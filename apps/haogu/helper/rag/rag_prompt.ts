import { PromptTemplate } from '@langchain/core/prompts'
import { IReasoningRagTool } from './reasoning_rag_tool'


export class RagPrompt {


  public static async isNeedReasoningRag(chatHistory: string, ragContext: string) {
    const template = `你是一个辅助决策的助手，负责根据对话历史和当前检索到的上下文判断是否需要进行详细的推理和工具调用来回答用户的问题。
在做出判断时：
- 关注核心意图的匹配度，不拘泥于具体表述。
- 允许同义、近义、隐含含义和概念泛化（如时间、数量、条件）来进行匹配。
- 如果上下文已能直接或通过简单推理覆盖用户的核心问题，则判定为 NO；只有在关键信息缺失时才判定为 YES。

当前对话历史：${chatHistory}
检索到的上下文：${ragContext}

【输出格式（只输出此 JSON）】
{{
  "reason": "说明判定依据",
  "verdict": "YES"（需要额外信息） 或 "NO"(不需要额外信息),
  "missing_points": "描述缺少的信息"
}}`

    const promptTemplate = PromptTemplate.fromTemplate(template)
    return promptTemplate.format({ chatHistory, ragContext })
  }


  /**
   * 生成 Reasoning RAG 回合的提示，用于指导 LLM 进行下一步推理、调用工具或给出最终答案。
   * @param chatHistory 与用户的对话历史。
   * @param context     当前上下文（包括前几步的思考、动作与观察）。
   * @param strategy    推理策略（例如 "react"、"cot" 等），可以用于提示中说明模型应如何思考。
   * @param tools       可用工具列表，每个工具包含 name 与 description。
   * @returns 用于 LLM 的提示字符串。
   */
  public static async reasoningRag(
    chatHistory: string,
    context: string,
    strategy: string,
    tools: IReasoningRagTool[]
  ): Promise<string> {
    const toolList = tools
      .map((t) => `* ${t.name}: ${t.description}`)
      .join('\n')


    const prompt = `你是一个采用 ReAct 框架的智能代理。在推理过程中，你需要交替输出自己的思考（thought）和要调用的工具（action）。每一步请严格按照以下格式返回一个 JSON 对象，其中包含下列键之一：
- "thought": 你的下一步思考内容。
- "action": 要调用的工具名称（必须出现在可用工具列表中）。
- "action_input": 传递给工具的输入。
- 如果你认为已经得出了最终答案，则不再调用工具，直接返回 {{"finish": "<最终答案>"}}。

当前策略: {strategy}
对话历史: {chatHistory}
历史上下文: {context}

可用工具：
{toolList}

请根据上述信息输出 JSON。示例：
{{"thought": "...", "action": "search", "action_input": {{}} }}
或
{{"finish": "最终答案..."}}

不要返回任何解释或非 JSON 内容。
`

    const promptTemplate = PromptTemplate.fromTemplate(prompt)


    return promptTemplate.format({ strategy, chatHistory, context, toolList })
  }



  public static async finalAnswer(chatHistory: string, context: string) {
    const prompt = `你是一名“精简问答助手”。仅依据以下输入生成用户问题的最终答案：

【聊天历史】
{chatHistory}

【推理过程与工具返回的额外信息】
{context}

任务：
- 通读以上两部分内容，提取与“用户最后一次提问”直接相关的关键信息，产出最终答案。

规则：
1) 只使用【聊天历史】与【额外信息】中的内容；不得引入任何外部知识或未在输入中出现的信息。
2) 不调用任何工具，不提出新问题，不复述本指令或你的推理过程。
3) 优先回答用户最后一次明确提问；如包含多个问题，仅回答输入中可确定且有依据的部分。
4) 语言简洁直接：通常 1–3 句；如需列点，最多 3 条。
5) 若信息不足以得出确定答案：用一句话说明“基于提供的信息无法确定答案”，并点明缺失的关键信息（只说明缺什么，不要求补充）。

输出格式：
- 仅输出答案正文；不添加前缀、标题、客套或提示语。`

    const promptTemplate = PromptTemplate.fromTemplate(prompt)

    return promptTemplate.format({ chatHistory, context })

  }





}