import express from 'express'
import axios from 'axios'
import logger from 'model/logger/logger'
import { IReceivedMessage, ISendMessageResult } from 'model/juzi/type'
import { JuziAPI } from 'model/juzi/api'
import { CacheDecorator } from 'lib/cache/cache'
import { JuziMessageHandler } from 'service/message_handler/juzi/message_handler'
import { ClientAccountConfig } from 'service/database/config'
import { initConfig } from './init'
import { catchGlobalError } from 'model/server/server'
import { EventHandler, sendHaoguWelComeMessage } from './event_handler'
import { ITestEventChangeNextStage, ITestEventClearCache } from './event_type'
import { handleImageMessage, handleUnknownMessage, handleVideoMessage } from './message_handler'
import { Config } from 'config'
import { Workflow } from '../workflow/workflow'
import { messageReplyServiceClient } from '../service/message_replay_service_instance'
import { receiveMessageCount } from '../prometheus/client'
import { register } from 'lib/prometheus_client/index'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { eventTrackClient, sendMessageResultHandlerClient } from '../service/send_message_instance'

const app = express()
const messageHandler = new JuziMessageHandler({
  handleClassGroupMessage: async () => {},
  handleUnknownMessage,
  handleImageMessage,
  handleVideoMessage,
  sendWelcomeMessage:sendHaoguWelComeMessage,
  workflow:Workflow
},
chatDBClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient, eventTrackClient)

app.use(express.json())

app.get('/', (req, res) => {
  logger.log('Hello Client, this is Server!')
  res.send('Hello Client!')
})

app.post('/message', async (req, res) => {
  // 接收消息
  const msg: IReceivedMessage = req.body

  messageHandler.handle(msg) // 添加到消息队列
  receiveMessageCount.labels({ bot_id:Config.setting.wechatConfig?.name || Config.setting.wechatConfig?.id }).inc(1)
  res.send('ok')
})

app.get('/metrics', async (req, res) => {
  res.status(200).set('Content-Type', register.contentType).send(await register.metrics())
})

app.post('/event', async (req, res) => {
  // 接收消息
  const data = req.body

  new EventHandler().handle(data)
  res.send('ok')
})

app.post('/sendResult', async (req, res) => {
  // 接收消息
  const data: ISendMessageResult = req.body

  sendMessageResultHandlerClient.handle(data) // 处理消息发送结果
  res.send('ok')
})

app.post('/test/event/change_stage', async (req, res) => {
  const data = req.body as ITestEventChangeNextStage

  await chatStateStoreClient.update(data.chatId, {
    nextStage: data.stage
  })

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event/clear_cache', async(req, res) => {
  const data = req.body as ITestEventClearCache

  chatStateStoreClient.clearCache(data.chatId)

  res.send({
    code: 200,
    msg: 'ok'
  })
})
// 缓存 API，提高性能
// JuziAPI.getCustomerInfo = CacheDecorator.decorateAsync(JuziAPI.getCustomerInfo)
JuziAPI.externalIdToWxId = CacheDecorator.decorateAsync(JuziAPI.externalIdToWxId)

catchGlobalError() // 防止 抛错，导致服务停止
initServer()

async function initServer() {
  const name = process.env.WECHAT_NAME

  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  await initConfig()

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  // 消息处理 Worker
  messageHandler.startWorker()

  app.listen(account.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${account.port}`)
  })
}
