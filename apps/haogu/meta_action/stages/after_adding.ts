import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { PostAction } from './post_action'

export class AfterAdding extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    return true
  }

  async getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return {
      '询问挖需第一题': PostAction.askQuestionOne,
      '询问挖需第二题': PostAction.askQuestionTwo,
      '测评股票段位为菜鸟闯荡生成评测结果': PostAction.askResultLevel1,
      '测评股票段位为小试牛刀生成评测结果': PostAction.askResultLevel2,
      '测评股票段位为进阶修炼生成评测结果': PostAction.askResultLevel3,
      '测评股票段位为老手精进生成评测结果': PostAction.askResultLevel4,
      '测评股票段位为理性操盘手生成评测结果': PostAction.askResultLevel5,
      '结束挖需并介绍高手的资金曲线': PostAction.sendEconomicCurve
    }
  }

  async getMetaAction(chatId: string): Promise<Record<string, string>> {
    return MetaActions.afterAdding
  }

  async getThinkPrompt(chatId: string): Promise<string> {
    return ThinkPrompt.afterAdding
  }

  async prepareActivation(chatId: string, roundId: string): Promise<void> {

  }

  async getGuidance(chatId: string): Promise<string> {
    return ''
  }
}