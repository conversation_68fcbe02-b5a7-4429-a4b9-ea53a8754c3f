import { HandleActionOption } from 'service/visualized_sop/visualized_sop_processor'

export const conditionJudgeMap:Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)> = {
}

export const textVariableMap:Record<string, (params:{chatId:string;userId:string})=> Promise<string>> = {
  '第一节课链接': async({ chatId }) => {
    return ''
  },
  '第二节课链接': async({ chatId }) => {
    return ''
  },
  '第三节课链接': async({ chatId }) => {
    return ''
  },
  '第四节课链接': async({ chatId }) => {
    return ''
  },
  '第五节课链接': async({ chatId }) => {
    return ''
  },
  '第六节课链接': async({ chatId }) => {
    return ''
  },
}

export const actionCustomMap:Record<string, (params:{chatId:string;userId:string;opt:HandleActionOption})=> Promise<void>> = {
}

export const linkSourceVariableTagMap:Record<string, (params:{chatId:string;userId:string})=>Promise<string>> = {
}
