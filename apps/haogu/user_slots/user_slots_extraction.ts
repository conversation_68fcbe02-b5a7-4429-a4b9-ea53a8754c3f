import { BaseExtractUserSlots } from 'service/user_slots/extract_user_slots'
import { chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'

const TopicRecommendations = `- 基本信息：称呼、客户年龄，性别
- 想要解决的问题`

const TopicRules = ''



export class ExtractUserSlots extends BaseExtractUserSlots {
  constructor() {
    super(chatHistoryServiceClient, chatStateStoreClient)
  }
  getTopicRules(): string {
    return TopicRules
  }

  getTopicRecommendations(): string {
    return TopicRecommendations
  }
}