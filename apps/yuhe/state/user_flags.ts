export interface IChattingFlag {
  [key:string]: boolean | string | number | undefined
  is_bind_phone?: boolean

  is_add_tasks?: boolean // 是否已经添加过任务
  is_friend_accepted?: boolean // 是否处理过添加好友

  handled_failed_payment?: boolean // 处理过支付失败

  is_send_course_intro?: boolean // 是否发送过课程介绍

  is_send_first_ask_intention?:boolean

  is_complete_douyin_analysis?: boolean
  is_complete_homework1?: boolean
  is_complete_homework2?: boolean
  is_complete_homework3?: boolean
  is_complete_homework4?: boolean

  is_ask_day1_replay?: boolean
  is_ask_day2_replay?: boolean
  is_ask_day3_replay?: boolean
  is_ask_day4_replay?: boolean

  is_complete_payment?: boolean // 是否完成支付
  is_before_payment?: boolean // 成交前，准支付

  // 完课礼
  is_send_course1_gift?: boolean
  is_send_course2_gift?: boolean
  is_send_course3_gift?: boolean
  is_send_course4_gift?: boolean

  // 回放
  is_send_course_replay_day1?: boolean
  is_send_course_replay_day2?: boolean
  is_send_course_replay_day3?: boolean
  is_send_course_replay_day4?: boolean

  // 到课
  is_attend_course_day1?: boolean
  is_attend_course_day2?: boolean
  is_attend_course_day3?: boolean
  is_attend_course_day4?: boolean

  // 完课
  is_complete_course_day1?: boolean
  is_complete_course_day2?: boolean
  is_complete_course_day3?: boolean
  is_complete_course_day4?: boolean

  is_in_live_room?: boolean // 是否在直播间

  is_invite_group_fail_after_payment?: boolean // 是否客户付款后邀请群失败

  is_in_postpone?: boolean // 是否在延期流程中
  has_postpone?: boolean // 是否已经延期
  has_refresher_training?: boolean // 是否已经进行过复训

  is_already_ask_douyin_screenshot?:boolean

  // 意向度相关字段
  intent_level_high?: boolean // 高意向客户
  intent_level_medium?: boolean // 中意向客户
  intent_level_low?: boolean // 低意向客户
  intent_level_zero?: boolean // 零意向客户

  // 延迟趋势分析相关字段
  latency_model_ready?: boolean // 延迟模型是否就绪
  latency_model_high_confidence?: boolean // 延迟模型是否高置信度
  latency_trend_increasing?: boolean // 延迟趋势是否增加
  latency_trend_decreasing?: boolean // 延迟趋势是否减少
  latency_trend_stable?: boolean // 延迟趋势是否稳定
  latency_trend_strong?: boolean // 延迟趋势强度是否强
  latency_trend_moderate?: boolean // 延迟趋势强度是否中等
  latency_trend_weak?: boolean // 延迟趋势强度是否弱

  // 沉默时长相关字段
  silence_warning?: boolean // 沉默警告
  silence_danger?: boolean // 沉默危险
  silence_critical?: boolean // 沉默严重

  payment_number?: number
  transactionIds?: string
}