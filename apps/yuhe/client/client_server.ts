import express from 'express'
import axios from 'axios'
import logger from 'model/logger/logger'
import { exec } from 'child_process'
import { IReceivedMessage, ISendMessageResult } from 'model/juzi/type'
import { JuziAPI } from 'model/juzi/api'
import { CacheDecorator } from 'lib/cache/cache'
import { JuziMessageHandler } from 'service/message_handler/juzi/message_handler'
import { ClientAccountConfig } from 'service/database/config'
import { initConfig } from './init'
import { catchGlobalError } from 'model/server/server'
import { EventHandler, sendYuHeWelComeMessage } from './event_handler'
import { IXingyanPushCallback } from 'model/xingyan'
import { ITestEventChangeNextStage, ITestEventClearCache } from './event_type'
import { handleImageMessage, handleUnknownMessage, handleVideoMessage } from './message_handler'
import { getUserId } from 'config/chat_id'
import { Config } from 'config'
import { Workflow } from '../workflow/workflow'
import { sendMessageResultHandlerClient } from '../service/instance'
import { PrismaMongoClient } from '../database/prisma'
import { messageReplyServiceClient } from '../service/message_replay_service_instance'
import { receiveMessageCount } from '../prometheus/client'
import { register } from 'lib/prometheus_client/index'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { eventTrackClient } from '../service/event_track_instance'

const app = express()
const messageHandler = new JuziMessageHandler({
  handleClassGroupMessage: async () => {},
  handleUnknownMessage,
  handleImageMessage,
  handleVideoMessage,
  sendWelcomeMessage:sendYuHeWelComeMessage,
  workflow:Workflow
},
chatDBClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient, eventTrackClient)

app.use(express.json())

app.get('/', (req, res) => {
  logger.log('Hello Client, this is Server!')
  res.send('Hello Client!')
})

app.post('/message', async (req, res) => {
  // 接收消息
  const msg: IReceivedMessage = req.body

  messageHandler.handle(msg) // 添加到消息队列
  receiveMessageCount.labels({ bot_id:Config.setting.wechatConfig?.name || Config.setting.wechatConfig?.id }).inc(1)
  res.send('ok')
})

app.get('/metrics', async (req, res) => {
  res.status(200).set('Content-Type', register.contentType).send(await register.metrics())
})

app.post('/event', async (req, res) => {
  // 接收消息
  const data = req.body

  new EventHandler().handle(data)
  res.send('ok')
})

app.post('/sendResult', async (req, res) => {
  // 接收消息
  const data: ISendMessageResult = req.body

  sendMessageResultHandlerClient.handle(data) // 处理消息发送结果
  res.send('ok')
})

app.post('/yuhe/event', async (req, res) => {
  new EventHandler().handleYuHeEvent(req.body as IXingyanPushCallback<any>)
  res.send('ok')
})

app.post('/test/event/change_stage', async (req, res) => {
  const data = req.body as ITestEventChangeNextStage

  await chatStateStoreClient.update(data.chatId, {
    nextStage: data.stage
  })

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event/clear_cache', async(req, res) => {
  const data = req.body as ITestEventClearCache

  chatStateStoreClient.clearCache(data.chatId)

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event/handle_invite_group_fail', async(req, res) => {
  const chatsJson = await PrismaMongoClient.getInstance().chat.findRaw({
    filter: {
      'chat_state.state.is_invite_group_fail_after_payment': true,
      'wx_id': Config.setting.wechatConfig?.id
    },
  })

  if (!chatsJson) {
    return res.send({
      code: 500,
      msg: '无法找到未拉群客户'
    })
  }

  const chats = chatsJson as unknown as any[]

  const failRes: string[] =  []

  for (let i = 0; i < chats.length; i++) {
    try {
      await EventHandler.inviteToGroup(chats[i]._id, getUserId(chats[i]._id))
      const chatState = await chatStateStoreClient.get(chats[i]._id)
      chatState.state.is_invite_group_fail_after_payment = false
      await chatDBClient.updateState(chats[i]._id, chatState)
      await chatStateStoreClient.clear(chats[i]._id)
    } catch (e) {
      failRes.push(chats[i]._id)
    }
  }

  if (failRes.length === 0) {
    res.send({
      code: 200,
      msg: '拉群成功'
    })
  } else {
    res.send({
      code: 200,
      msg: '拉群部分成功'
    })
  }

})

// 缓存 API，提高性能
// JuziAPI.getCustomerInfo = CacheDecorator.decorateAsync(JuziAPI.getCustomerInfo)
JuziAPI.externalIdToWxId = CacheDecorator.decorateAsync(JuziAPI.externalIdToWxId)

catchGlobalError() // 防止 抛错，导致服务停止
initServer()

async function initServer() {
  const name = process.env.WECHAT_NAME

  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  await initConfig()

  // 消息处理 Worker
  messageHandler.startWorker()

  app.listen(account.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${account.port}`)
  })
}


// 测试账号自动化部署
app.post('/webhook', async (req, res) => {
  const payload = req.body
  console.log(JSON.stringify(payload, null, 4))

  // 检查是否为开发分支推送
  if (payload.ref !== 'refs/heads/develop') {
    return res.status(200).json({ message: 'Not moer branch push, ignoring.' })
  }

  // 执行部署脚本
  try {
    // 使用 Promise 封装异步子进程执行
    const runCommand = (command: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            // 创建包含完整错误信息的错误对象
            const enhancedError = new Error(`Command failed: ${command}`)
            enhancedError.message = `Command: ${command}\nExit code: ${error.code}\nStdout: ${stdout}\nStderr: ${stderr}\nOriginal error: ${error.message}`
            reject(enhancedError)
            return
          }
          resolve(stdout)
        })
      })
    }
    // 执行命令

    await runCommand('git pull')
    await runCommand('pnpm install')
    await runCommand('pnpm dlx turbo prisma_generate')
    try {
      await runCommand('pnpm dlx turbo tsc-check')
    } catch (e) {
      // 记录详细的 TypeScript 检查错误信息
      logger.error('TypeScript check failed:', e instanceof Error ? e.message : e)

      // 直接发短信提示报错
      await axios.get(`https://fwalert.com/d675887f-7151-4bb1-9da4-fdb7000c9c23?user=${encodeURIComponent(payload.user.name)}`)
    }
    await runCommand('fuser -k 4097/tcp')

    res.status(200).json({ message: 'Deployment script executed successfully.' })
  } catch (error) {
    logger.error(error)
    res.status(500).json({
      message: 'Deployment failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})