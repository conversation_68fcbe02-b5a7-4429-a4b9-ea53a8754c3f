import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { PostAction } from './post_action'
import { IChattingFlag } from '../../state/user_flags'
import { MetaActions, ThinkPrompt } from '../meta_action'
import logger from 'model/logger/logger'
import { DataService } from '../../helper/getter/get_data'
import { DateHelper } from 'lib/date/date'
import dayjs from 'dayjs'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { getUserId } from 'config/chat_id'
import { Config } from 'config/'
import { catchError } from 'lib/error/catchError'
import { JuziAPI } from 'model/juzi/api'
import { chatStateStoreClient, chatDBClient } from '../../service/base_instance'

export class CoursePostpone extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const isInPostpone = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_in_postpone
    if (isInPostpone) return Promise.resolve(true)
    return Promise.resolve(false)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '执行延期': CoursePostpone.executePostpone,
      '退出延期': CoursePostpone.exitPostpone,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.coursePostpone)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.coursePostpone)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve()
  }

  async getGuidance(chatId: string): Promise<string> {
    const dayDiff = await PostAction.getNewCourseStartDayDiff(chatId)
    const date = DateHelper.add(new Date(), dayDiff, 'day')
    return Promise.resolve(`可供延期/复训的时间：${DateHelper.getFormattedDate(date, false)}`)
  }

  private static async exitPostpone(chatId: string) {
    logger.trace({ chat_id: chatId }, '客户退出延期')

    await chatStateStoreClient.update(chatId, {
      state: <IChattingFlag> {
        is_in_postpone: false,
      },
    })
    // 重启sop
    await chatDBClient.setStopGroupPush(chatId, false)
    return { guidance: '' } as IActionInfo
  }

  private static async executePostpone(chatId: string) {
    logger.trace({ chat_id: chatId }, '客户确认延期')
    const userId = getUserId(chatId)
    const oldCourseNo = await DataService.getCourseNoByChatId(chatId)


    await chatStateStoreClient.update(chatId, {
      state: <IChattingFlag> {
        is_in_postpone: false,
      },
    })

    const currentTime = await DataService.getCurrentTime(chatId)
    // 延期
    if (currentTime.day < 5) {
      await chatStateStoreClient.update(chatId, {
        state: <IChattingFlag> {
          has_postpone: true,
        },
      })
    } else { //复训
      await chatStateStoreClient.update(chatId, {
        state: <IChattingFlag> {
          has_refresher_training: true,
        },
      })
    }
    // 重启sop
    await chatDBClient.setStopGroupPush(chatId, false)

    const dayDiff = await PostAction.getNewCourseStartDayDiff(chatId) - 1
    const date = DateHelper.add(new Date(), dayDiff, 'day')
    const newCourseNo = dayjs(date).format('YYYYMMDD')

    await DataService.delayCourseNo(chatId, Number(newCourseNo))

    if (currentTime.day < 5) {
      await HumanTransfer.transfer(chatId, userId, HumanTransferType.ExecutePostpone, 'onlyNotify', `新期数：${newCourseNo}`)
      await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId, `${oldCourseNo}延期${newCourseNo}`))
    } else {
      await HumanTransfer.transfer(chatId, userId, HumanTransferType.ExecuteRefresherTraining, 'onlyNotify', `新期数：${newCourseNo}`)
      await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId, `${oldCourseNo}复训${newCourseNo}`))
    }


    return { guidance: '' } as IActionInfo
  }

}