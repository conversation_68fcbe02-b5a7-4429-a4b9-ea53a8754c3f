import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { PostAction } from './post_action'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { chatStateStoreClient } from '../../service/base_instance'


export class AfterBonding extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const nodeCount = await chatStateStoreClient.getNodeCount(chatId, 'FreeTalk')

    return Promise.resolve(nodeCount > 15)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    }
    return Promise.resolve(actionMap)
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.AfterBonding)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterBonding)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }
}