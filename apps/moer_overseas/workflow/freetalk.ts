import { ContextBuilder } from './context'
import { freeThinkClient } from '../service/instance'
import { IWorkflowState } from 'service/llm/state'
import { MetaActionRouter } from 'service/agent/meta_action/router'
import { Node } from './nodes/types'
import { Reply } from './reply'
import { trackInvoke, WorkFlowNode } from './nodes/base_node'
import { UserLanguage } from '../helper/language/user_language_verify'

import { AfterAdding } from '../meta_action/stages/after_adding'
import { AfterBonding } from '../meta_action/stages/after_bonding'
import { AfterCourse1 } from '../meta_action/stages/after_course1'
import { AfterCourse3 } from '../meta_action/stages/after_course3'
import { AfterCourse4 } from '../meta_action/stages/after_course4'
import { AfterCourseWeek } from '../meta_action/stages/after_courseweek'
import { AfterPaid } from '../meta_action/stages/after_paid'
import { DuringCourse } from '../meta_action/stages/during_course'

const components = [
  new AfterPaid(),
  new DuringCourse(),
  new AfterCourseWeek(),
  new AfterCourse4(),
  new AfterCourse3(),
  new AfterCourse1(),
  new AfterBonding(),
  new AfterAdding(),
]  // 必须严格按照流程倒序添加

export class FreeTalk extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const metaActionRouter = new MetaActionRouter(components)
    const metaActionStage = await metaActionRouter.getThinkAndMetaActions(state.chat_id, state.round_id)
    const contextComponent = new ContextBuilder({ state })
    const { action, strategy } = await freeThinkClient.invoke(
      state,
      metaActionStage.thinkPrompt,
      metaActionStage.metaActions,
      await contextComponent.customerBehavior(state.chat_id),
      await contextComponent.customerPortrait(state.chat_id),
      await contextComponent.temporalInformation(state.chat_id),
    )

    const actionInfo = await metaActionRouter.handleAction(state.chat_id, state.round_id, action)
    const languageOption = await UserLanguage.getLanguageSetting(state.chat_id)
    const talkStrategyPrompt = [
      '无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止',
      strategy,
      metaActionStage.guidance,
      actionInfo.guidance,
      languageOption
    ].filter(Boolean).join('\n')

    const context = await ContextBuilder.build({
      state,
      retrievedKnowledge: true,
      customerMemory: true,
      talkStrategyPrompt: talkStrategyPrompt,
    })

    await Reply.invoke({
      state,
      model: 'gpt-5-chat',
      context: context,
      temperature: 0.8,
      maxTokens: 400,
      promptName: 'free_talk',
      postReplyCallBack: actionInfo.callback
    })
    return Node.FreeTalk
  }
}