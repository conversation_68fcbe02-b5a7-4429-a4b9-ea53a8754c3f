import { changeCourseNo, changeNextStage, changePhone, clearCache, queryChatById, updateIp, updateIsInviteGroupAfterPayment, updateIsPaid, updatePaymentNumber, updatePayTime } from '@/app/yuhe/api/chat'
import { Node } from 'yuhe/workflow/nodes/types'
import { resetSop } from '@/app/yuhe/api/sop'
import UserEdit from '@/app/yuhe/component/edit'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={param.id}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    changePhone={changePhone}
    stageOption={Object.values(Node)}
    updateIsPaid={updateIsPaid}
    updateIsInviteGroupAfterPayment={updateIsInviteGroupAfterPayment}
    updateIp={updateIp}
    updatePayTime={updatePayTime}
    clearCache={clearCache}
    resetSop={resetSop}
    updatePaymentNumber={updatePaymentNumber}
  />
}