'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'
import axios from 'axios'
import dayjs from 'dayjs'
import { Config } from 'config'
import { HaoguUserData } from '../type/user'
import { chatStateStoreClient } from 'haogu/service/instance'
import { IChattingFlag } from 'haogu/state/user_flags'

Config.setting.projectName = 'haogu'

export async function queryChats(nameOrPhone:string, courseNo?:number):Promise<HaoguUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:200,
    orderBy:{
      created_at:'desc'
    },
    where:{
      AND:[
        {
          course_no:courseNo,
        },
        {
          OR:[
            { phone:{ contains:nameOrPhone } },
            { id:nameOrPhone },
            { contact:{
              is:{
                wx_name: {
                  contains: nameOrPhone
                }
              }
            }
            }
          ]
        }
      ]
    } })
  return queryNameResult as unknown as HaoguUserData[]
}

export async function queryDefaultChats() {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:10,
    orderBy: {
      created_at: 'desc' // 按照 create_at 降序排列
    },
  })
  return queryNameResult as unknown as HaoguUserData[]
}

export async function queryChatById(id:string):Promise<HaoguUserData | null> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const result = await mongoClient.chat.findFirst({ where:{
    id
  } })
  return result as unknown as (HaoguUserData | null)
}

export async function queryChatsWithoutAi(courseNo?:number): Promise<HaoguUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const result = await mongoClient.chat.findMany({ where:{
    is_human_involved:true,
    course_no:courseNo
  },
  take: courseNo ? undefined : 50
  })
  return result as unknown as HaoguUserData[]
}

export async function queryChatsWithoutPhone(courseNo?:number):Promise<HaoguUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const result = await mongoClient.chat.findRaw({ filter: { phone: { $exists: false }, course_no: courseNo }, options:{
    limit: courseNo ? undefined : 50
  } }) as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }
  return result
}

export async function changeIsHumanInvolved(chatId:string, isHumanInvolved:boolean) {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_human_involved:isHumanInvolved } })
}

export async function changeIsStopGroupPush(chatId:string, isStopGroupPush:boolean) {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_stop_group_push:isStopGroupPush } })
}

export async function changeCourseNo(chatId:string, courseNo:number) {
  throw ('not implement')
}

export async function changePhone(chatId:string, phone:string) {
  throw ('not implement')
}

export async function changeNextStage(chatId:string, stage:Node) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = AdminPrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/test/event/change_stage`, {
    method:'POST',
    data:{
      chatId: chatId,
      stage: stage
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function clearCache(chatId:string):Promise<void> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = AdminPrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/test/event/clear_cache`, {
    method:'POST',
    data:{
      chatId: chatId,
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function getChatByCourseWeekRange(
  minCourseWeek: number,
  maxCourseWeek: number
) {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const chatList = await mongoClient.chat.findMany({
    where: {
      course_no: {
        gte: minCourseWeek,
        lte: maxCourseWeek,
      },
    },
  })
  return chatList
}

export async function updateIsPaid(chatId:string, isPaid:boolean): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_complete_payment': isPaid } }
      }
    ]
  })
}

export async function updateIsInviteGroupAfterPayment(chatId:string, isInviteGroupFailAfterPayment:boolean): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_invite_group_fail_after_payment': isInviteGroupFailAfterPayment } }
      }
    ]
  })
}

export async function updatePayTime(chatId:string, time:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ pay_time:dayjs(time).toDate() } })
}

export async function updatePaymentNumber(chatId:string, number:number):Promise<void> {
  chatStateStoreClient.clearCache(chatId)
  await chatStateStoreClient.update(chatId, {
    state:<IChattingFlag>{
      payment_number:number
    }
  })
  chatStateStoreClient.clearCache(chatId)
}