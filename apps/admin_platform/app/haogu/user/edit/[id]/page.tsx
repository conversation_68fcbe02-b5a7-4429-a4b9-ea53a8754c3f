import { Node } from 'haogu/workflow/nodes/types'
import { changeCourseNo, changeNextStage, changePhone, clearCache, queryChatById, updateIsPaid, updatePayTime } from '@/app/haogu/api/chat'
import UserEdit from '@/app/haogu/component/edit'
import { resetSop } from '@/app/haogu/api/sop'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={param.id}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    changePhone={changePhone}
    stageOption={Object.values(Node)}
    updateIsPaid={updateIsPaid}
    clearCache={clearCache}
    resetSop={resetSop}
    updatePayTime={updatePayTime}
  />
}